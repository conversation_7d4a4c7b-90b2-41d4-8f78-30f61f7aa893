import React, { useState, useEffect } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, Modal } from 'react-native';
import { Appbar, Button, IconButton, Chip, Portal, Snackbar } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import LoadingAnimation from '@/components/LoadingAnimation';
import GroceryListItem from '@/components/GroceryListItem';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { GroceryItem, Tag } from '@/components/types';
import { useGroceryList } from '@/contexts/GroceryListContext';
import ShareButton, { ShareButtonType, ShareButtonVariant } from '@/components/ShareButton';

export default function GroceryListScreen() {
  const colorScheme = useColorScheme() || 'light';
  const router = useRouter();
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  const [groceryItems, setGroceryItems] = useState<GroceryItem[]>([]);
  const [newItemText, setNewItemText] = useState('');
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [isTagModalVisible, setIsTagModalVisible] = useState(false);
  const [editingItemName, setEditingItemName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const {
    updateGroceryItemCount,
    addItem,
    toggleItemChecked,
    updateItemTags,
    incrementItemQuantity,
    decrementItemQuantity,
    removeItem,
    isUserAuthenticated,
  } = useGroceryList();

  // Load grocery list data when user is authenticated
  useEffect(() => {
    if (isUserAuthenticated) {
      loadGroceryList();
    }
  }, [isUserAuthenticated]);

  const loadGroceryList = async () => {
    try {
      setIsLoading(true);
      const items = await updateGroceryItemCount();
      // Ensure all items have a quantity field (for backward compatibility)
      const itemsWithQuantity = items.map((item) => ({
        ...item,
        quantity: item.quantity || 1,
      }));
      setGroceryItems(itemsWithQuantity);
    } catch (error) {
      console.error('Error loading grocery list:', error);
      showMessage('Failed to load grocery list');
    } finally {
      setIsLoading(false);
    }
  };

  const showMessage = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleAddItem = async () => {
    if (newItemText.trim() === '') return;

    try {
      const itemName = newItemText.trim();
      const updatedItems = await addItem(itemName, selectedTags);
      setGroceryItems(updatedItems);
      setNewItemText('');
      setSelectedTags([]);
    } catch (error) {
      console.error('Error adding item to grocery list:', error);
      showMessage('Failed to add item to grocery list');
    }
  };

  const openTagModal = (itemName?: string) => {
    if (itemName) {
      const item = groceryItems.find((item) => item.name === itemName);
      if (item) {
        setSelectedTags(item.tags || []);
        setEditingItemName(itemName);
      }
    } else {
      setEditingItemName(null);
    }
    setIsTagModalVisible(true);
  };

  const closeTagModal = () => {
    setIsTagModalVisible(false);
  };

  const toggleTag = (tag: Tag) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((t) => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const saveTagsToItem = async () => {
    if (editingItemName) {
      try {
        const updatedItems = await updateItemTags(editingItemName, selectedTags);
        setGroceryItems(updatedItems);
      } catch (error) {
        console.error('Error updating item tags:', error);
        showMessage('Failed to update tags');
      }
    }
    closeTagModal();
  };

  const toggleItemCheck = async (name: string) => {
    try {
      const updatedItems = await toggleItemChecked(name);
      setGroceryItems(updatedItems);
    } catch (error) {
      console.error('Error toggling item checked status:', error);
      showMessage('Failed to update item');
    }
  };

  const handleIncrement = async (itemName: string) => {
    try {
      const updatedItems = await incrementItemQuantity(itemName);
      setGroceryItems(updatedItems);
    } catch (error) {
      console.error('Error incrementing item quantity:', error);
      showMessage('Failed to update quantity');
    }
  };

  const handleDecrement = async (itemName: string) => {
    try {
      const updatedItems = await decrementItemQuantity(itemName);
      setGroceryItems(updatedItems);
    } catch (error) {
      console.error('Error decrementing item quantity:', error);
      showMessage('Failed to update quantity');
    }
  };

  const handleRemoveItem = async (itemName: string) => {
    try {
      const updatedItems = await removeItem(itemName);
      setGroceryItems(updatedItems);
      showMessage(`Removed ${itemName} from grocery list`);
    } catch (error) {
      console.error('Error removing item:', error);
      showMessage('Failed to remove item');
    }
  };

  const handleShareError = (error: Error) => {
    if (error.message === 'No unchecked items to share') {
      showMessage('No unchecked items to share');
    } else {
      showMessage('Failed to share grocery list');
    }
  };

  const renderEmptyState = () => (
    <View style={[styles.emptyStateContainer, { backgroundColor: colors.background }]}>
      <Text style={[styles.emptyStateTitle, { color: colors.text }]}>Your grocery list is empty</Text>
      <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
        Add items to your grocery list using the field below.
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Appbar.Header style={[styles.header, { backgroundColor: colors.background }]}>
        <Appbar.BackAction onPress={() => router.back()} color={colors.text} />
        <Appbar.Content title='Grocery List' titleStyle={{ color: colors.text }} />
        {groceryItems.length > 0 && (
          <ShareButton
            type={ShareButtonType.GROCERY_LIST}
            variant={ShareButtonVariant.APPBAR_ACTION}
            groceryItems={groceryItems}
            onError={handleShareError}
            color={colors.text}
          />
        )}
      </Appbar.Header>

      {isLoading ? (
        <LoadingAnimation
          source={require('../assets/images/gifs/bounce-veggie.gif')}
          message='Loading your grocery list...'
        />
      ) : (
        <KeyboardAwareScrollView
          enableOnAndroid={true}
          enableAutomaticScroll={true}
          extraScrollHeight={100}
          keyboardShouldPersistTaps='always'
          style={{ flex: 1 }}
        >
          {groceryItems.length === 0 ? (
            renderEmptyState()
          ) : (
            <ScrollView style={styles.content} keyboardShouldPersistTaps='always'>
              {groceryItems.map((item) => (
                <GroceryListItem
                  key={item.name}
                  item={item}
                  onToggleChecked={toggleItemCheck}
                  onIncrement={handleIncrement}
                  onDecrement={handleDecrement}
                  onRemove={handleRemoveItem}
                  onOpenTagModal={openTagModal}
                />
              ))}
            </ScrollView>
          )}

          <View style={[styles.addItemContainer, { borderTopColor: colors.divider }]}>
            <View style={styles.inputRow}>
              <TextInput
                style={[styles.input, { color: colors.text, borderColor: colors.divider }]}
                placeholder='Add an item to your grocery list'
                placeholderTextColor={colors.textSecondary}
                value={newItemText}
                onChangeText={setNewItemText}
                onSubmitEditing={handleAddItem}
              />
              <IconButton
                icon='tag-outline'
                size={24}
                iconColor={colors.accent}
                onPress={() => openTagModal()}
                style={styles.tagButton}
              />
            </View>

            {selectedTags.length > 0 && (
              <View style={styles.tagsContainer}>
                {selectedTags.map((tag) => (
                  <Chip
                    key={tag}
                    style={[styles.tagChip, { backgroundColor: colors.selectionBackground }]}
                    textStyle={{ color: colors.selectionText }}
                    onClose={() => setSelectedTags(selectedTags.filter((t) => t !== tag))}
                    compact
                  >
                    {tag}
                  </Chip>
                ))}
              </View>
            )}

            <Button
              mode='contained'
              onPress={handleAddItem}
              buttonColor={colors.accent}
              textColor={colors.accentText}
              style={styles.addButton}
              disabled={!newItemText.trim()}
            >
              Add
            </Button>
          </View>

          <Portal>
            <Modal visible={isTagModalVisible} onDismiss={closeTagModal} style={styles.modalOverlay}>
              <View style={[styles.modalContainer, { backgroundColor: colors.surface }]}>
                <Text style={[styles.modalTitle, { color: colors.text }]}>Select Tags</Text>
                <ScrollView style={styles.tagScrollView}>
                  <View style={styles.tagSelectionContainer}>
                    {Object.values(Tag).map((tag) => (
                      <Chip
                        key={tag}
                        selected={selectedTags.includes(tag)}
                        onPress={() => toggleTag(tag)}
                        style={[
                          styles.selectableTagChip,
                          selectedTags.includes(tag)
                            ? { backgroundColor: colors.accent }
                            : { backgroundColor: colors.selectionBackground },
                        ]}
                        textStyle={{
                          color: selectedTags.includes(tag) ? colors.accentText : colors.selectionText,
                        }}
                      >
                        {tag}
                      </Chip>
                    ))}
                  </View>
                </ScrollView>
                <View style={styles.modalButtonContainer}>
                  <Button mode='text' onPress={closeTagModal} textColor={colors.textSecondary}>
                    Cancel
                  </Button>
                  <Button
                    mode='contained'
                    onPress={editingItemName ? saveTagsToItem : closeTagModal}
                    buttonColor={colors.accent}
                    textColor={colors.accentText}
                  >
                    {editingItemName ? 'Save' : 'Done'}
                  </Button>
                </View>
              </View>
            </Modal>
          </Portal>
        </KeyboardAwareScrollView>
      )}

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={{ marginBottom: 80 }}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    elevation: 0,
    shadowOpacity: 0,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyStateContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  itemContainer: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 4,
  },
  checkedItemText: {
    textDecorationLine: 'line-through',
    opacity: 0.7,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tagChip: {
    padding: 1,
    borderRadius: 20,
  },
  addItemContainer: {
    padding: 16,
    borderTopWidth: 1,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingRight: 40,
  },
  tagButton: {
    position: 'absolute',
    right: 0,
    margin: 0,
  },
  itemTagButton: {
    margin: 0,
    marginLeft: 'auto',
  },
  addButton: {
    borderRadius: 4,
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    marginVertical: 50,
    marginHorizontal: 16,
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  tagScrollView: {
    maxHeight: 400,
  },
  tagSelectionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    paddingBottom: 16,
  },
  selectableTagChip: {
    margin: 4,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 8,
  },
});
