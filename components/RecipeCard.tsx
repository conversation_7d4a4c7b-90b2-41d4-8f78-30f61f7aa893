import React from 'react';
import { View, Image, useColorScheme } from 'react-native';
import { Card, Text, IconButton } from 'react-native-paper';
import createRecipeStyles from '@/styles/RecipeStyles';
import { getThemeColors } from '@/styles/Theme';
import { useShare } from '@/hooks/useShare';
import { Recipe } from '@/components/types';
import { Ionicons } from '@expo/vector-icons';

interface RecipeCardProps {
  title: string;
  timeInMinutes: number;
  calories: number;
  imageUrl: string;
  isFavorite: boolean;
  recipe: Recipe;
  onToggleFavorite: (e: any) => void;
  onPress: () => void;
}

const RecipeCard: React.FC<RecipeCardProps> = ({
  title,
  timeInMinutes,
  calories,
  imageUrl,
  isFavorite,
  recipe,
  onToggleFavorite,
  onPress,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const RecipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const { shareBasicRecipe } = useShare();

  const handleShare = async (e: any) => {
    e.stopPropagation();
    try {
      await shareBasicRecipe(recipe);
    } catch (error) {
      console.error('Error sharing recipe:', error);
    }
  };

  return (
    <Card style={RecipeStyles.recipeCard} onPress={onPress}>
      <View style={RecipeStyles.recipeCardContent}>
        <View style={RecipeStyles.recipeHeader}>
          <View style={RecipeStyles.recipeHeaderContent}>
            <Text style={RecipeStyles.recipeTitle} numberOfLines={2}>
              {title}
            </Text>
            <Text style={RecipeStyles.recipeInfo}>
              {timeInMinutes} min · {calories} calories per serving
            </Text>
          </View>
          <View style={RecipeStyles.recipeActionButtons}>
            <IconButton
              icon={isFavorite ? 'heart' : 'heart-outline'}
              iconColor={colors.accent}
              size={24}
              onPress={(e) => {
                e.stopPropagation();
                onToggleFavorite(e);
              }}
              style={RecipeStyles.favoriteButton}
            />
            <Ionicons name='share-outline' color={colors.accent} size={24} onPress={handleShare} />
          </View>
        </View>
        <View style={RecipeStyles.imageContainer}>
          <Image source={{ uri: imageUrl }} style={RecipeStyles.recipeImage} resizeMode='cover' />
        </View>
      </View>
    </Card>
  );
};

export default RecipeCard;
