import React from 'react';
import { View, Text, useColorScheme } from 'react-native';
import { IconButton, Chip } from 'react-native-paper';
import BouncyCheckbox from "react-native-bouncy-checkbox";
import { getThemeColors } from '@/styles/Theme';
import { GroceryItem, Tag } from '@/components/types';
import { StyleSheet } from 'react-native';

interface GroceryListItemProps {
  item: GroceryItem;
  onToggleChecked: (itemName: string) => void;
  onIncrement: (itemName: string) => void;
  onDecrement: (itemName: string) => void;
  onRemove: (itemName: string) => void;
  onOpenTagModal: (itemName: string) => void;
}

const GroceryListItem: React.FC<GroceryListItemProps> = ({
  item,
  onToggleChecked,
  onIncrement,
  onDecrement,
  onRemove,
  onOpenTagModal,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  const handleDecrement = () => {
    if (item.quantity <= 1) {
      onRemove(item.name);
    } else {
      onDecrement(item.name);
    }
  };

  return (
    <View style={styles.itemContainer}>
      <View style={styles.itemRow}>
        <BouncyCheckbox
          isChecked={item.checked}
          onPress={() => onToggleChecked(item.name)}
          text={item.name}
          textStyle={{ color: colors.text }}
          fillColor={colors.accent}
          textContainerStyle={{
            width: '30%',
          }}
        />
        
        <View style={styles.rightControls}>
          <IconButton
            icon='tag-outline'
            size={20}
            iconColor={colors.accent}
            onPress={() => onOpenTagModal(item.name)}
            style={styles.tagButton}
          />
          
          <View style={styles.quantityControls}>
            <IconButton
              icon="trash-can-outline"
              size={20}
              iconColor={colors.textSecondary}
              onPress={() => onRemove(item.name)}
              style={styles.iconButton}
            />
            <IconButton
              icon="minus"
              size={20}
              iconColor={colors.textSecondary}
              onPress={handleDecrement}
              style={styles.iconButton}
            />
            <Text style={[styles.quantityText, { color: colors.text }]}>
              {item.quantity}
            </Text>
            <IconButton
              icon="plus"
              size={20}
              iconColor={colors.accent}
              onPress={() => onIncrement(item.name)}
              style={styles.iconButton}
            />
          </View>
        </View>
      </View>
      
      {item.tags && item.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {item.tags.map((tag) => (
            <Chip
              key={tag}
              style={[styles.tagChip, { backgroundColor: colors.selectionBackground }]}
              textStyle={{ color: colors.selectionText }}
              compact
            >
              {tag}
            </Chip>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    marginBottom: 8,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  rightControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagButton: {
    padding: 8,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '500',
    marginHorizontal: 8,
    minWidth: 20,
    textAlign: 'center',
  },
  iconButton: {
    padding: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    marginLeft: 40, // Align with checkbox text
  },
  tagChip: {
    marginRight: 8,
    marginBottom: 4,
  },
});

export default GroceryListItem;
