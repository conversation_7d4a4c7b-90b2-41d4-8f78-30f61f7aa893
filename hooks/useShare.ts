import { useCallback } from 'react';
import Share from 'react-native-share';
import { GroceryItem, Recipe, InstructionType } from '@/components/types';

export const useShare = () => {
  /**
   * Share unchecked grocery items
   */
  const shareGroceryList = useCallback(async (groceryItems: GroceryItem[]): Promise<void> => {
    try {
      // Filter unchecked items
      const uncheckedItems = groceryItems.filter(item => !item.checked);
      
      if (uncheckedItems.length === 0) {
        throw new Error('No unchecked items to share');
      }

      const shareContent = formatGroceryListForSharing(uncheckedItems);

      const shareOptions = {
        title: 'My Grocery List',
        message: shareContent,
        subject: 'Check out my grocery list!',
      };

      await Share.open(shareOptions);
    } catch (error) {
      // User cancelled sharing or an error occurred
      if (error instanceof Error && error.message !== 'User did not share') {
        console.error('Error sharing grocery list:', error);
        throw error;
      }
    }
  }, []);

  /**
   * Share a recipe
   */
  const shareRecipe = useCallback(async (
    recipe: Recipe,
    instructionType: InstructionType = InstructionType.DETAILED,
    servings: number = 1
  ): Promise<void> => {
    try {
      const shareContent = formatRecipeForSharing(recipe, instructionType, servings);

      const shareOptions = {
        title: `${recipe.title} Recipe`,
        message: shareContent,
        subject: `Check out this ${recipe.title} recipe!`,
      };

      await Share.open(shareOptions);
    } catch (error) {
      if (error instanceof Error && error.message !== 'User did not share') {
        console.error('Error sharing recipe:', error);
        throw error;
      }
    }
  }, []);

  /**
   * Share basic recipe info
   */
  const shareBasicRecipe = useCallback(async (recipe: Recipe): Promise<void> => {
    try {
      const shareContent = formatBasicRecipeForSharing(recipe);

      const shareOptions = {
        title: `${recipe.title} Recipe`,
        message: shareContent,
        subject: `Check out this ${recipe.title} recipe!`,
      };

      await Share.open(shareOptions);
    } catch (error) {
      if (error instanceof Error && error.message !== 'User did not share') {
        console.error('Error sharing basic recipe:', error);
        throw error;
      }
    }
  }, []);

  return {
    shareGroceryList,
    shareRecipe,
    shareBasicRecipe,
  };
};

/**
 * Format grocery list for sharing
 */
const formatGroceryListForSharing = (groceryItems: GroceryItem[]): string => {
  const itemsList = groceryItems
    .map((item) => {
      const quantityText = item.quantity > 1 ? ` (${item.quantity})` : '';
      const tagsText = item.tags.length > 0 ? ` - ${item.tags.join(', ')}` : '';
      return `• ${item.name}${quantityText}${tagsText}`;
    })
    .join('\n');

  const totalItems = groceryItems.reduce((sum, item) => sum + item.quantity, 0);

  return `🛒 My Grocery List

📝 ITEMS TO BUY (${totalItems} items):
${itemsList}

---
Shared from the ChefPal app 👨‍🍳`;
};

/**
 * Format recipe for sharing
 */
const formatRecipeForSharing = (recipe: Recipe, instructionType: InstructionType, servings: number): string => {
  const { title, timeInMinutes, calories, ingredients, instructions } = recipe;

  // Format ingredients list
  const ingredientsList = ingredients.map((ingredient) => `• ${ingredient.name}`).join('\n');

  // Get the appropriate instructions based on type
  const recipeInstructions = instructions[instructionType] || instructions[InstructionType.DETAILED];

  // Format the complete recipe
  return `🍽️ ${title}

⏱️ Time: ${timeInMinutes} minutes
🔥 Calories: ${calories} per serving
👥 Servings: ${servings}

📝 INGREDIENTS:
${ingredientsList}

👨‍🍳 INSTRUCTIONS:
${recipeInstructions}

---
Shared from the ChefPal app 👨‍🍳`;
};

/**
 * Format basic recipe for sharing (from ShareService)
 */
const formatBasicRecipeForSharing = (recipe: Recipe): string => {
  const { title, timeInMinutes, calories } = recipe;

  return `🍽️ ${title}

⏱️ Time: ${timeInMinutes} minutes
🔥 Calories: ${calories} per serving

This looks delicious! Get the full recipe and more cooking tips with ChefPal 👨‍🍳`;
};
