# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_FOLD_RIGHT_512_HPP
# define BOOST_PREPROCESSOR_SEQ_FOLD_RIGHT_512_HPP
#
# define BOOST_PP_SEQ_FOLD_RIGHT_257(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_257(op, st, BOOST_PP_SEQ_REVERSE_S(258, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_258(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_258(op, st, BOOST_PP_SEQ_REVERSE_S(259, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_259(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_259(op, st, BOOST_PP_SEQ_REVERSE_S(260, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_260(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_260(op, st, BOOST_PP_SEQ_REVERSE_S(261, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_261(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_261(op, st, BOOST_PP_SEQ_REVERSE_S(262, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_262(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_262(op, st, BOOST_PP_SEQ_REVERSE_S(263, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_263(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_263(op, st, BOOST_PP_SEQ_REVERSE_S(264, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_264(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_264(op, st, BOOST_PP_SEQ_REVERSE_S(265, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_265(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_265(op, st, BOOST_PP_SEQ_REVERSE_S(266, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_266(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_266(op, st, BOOST_PP_SEQ_REVERSE_S(267, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_267(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_267(op, st, BOOST_PP_SEQ_REVERSE_S(268, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_268(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_268(op, st, BOOST_PP_SEQ_REVERSE_S(269, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_269(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_269(op, st, BOOST_PP_SEQ_REVERSE_S(270, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_270(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_270(op, st, BOOST_PP_SEQ_REVERSE_S(271, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_271(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_271(op, st, BOOST_PP_SEQ_REVERSE_S(272, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_272(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_272(op, st, BOOST_PP_SEQ_REVERSE_S(273, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_273(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_273(op, st, BOOST_PP_SEQ_REVERSE_S(274, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_274(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_274(op, st, BOOST_PP_SEQ_REVERSE_S(275, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_275(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_275(op, st, BOOST_PP_SEQ_REVERSE_S(276, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_276(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_276(op, st, BOOST_PP_SEQ_REVERSE_S(277, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_277(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_277(op, st, BOOST_PP_SEQ_REVERSE_S(278, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_278(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_278(op, st, BOOST_PP_SEQ_REVERSE_S(279, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_279(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_279(op, st, BOOST_PP_SEQ_REVERSE_S(280, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_280(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_280(op, st, BOOST_PP_SEQ_REVERSE_S(281, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_281(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_281(op, st, BOOST_PP_SEQ_REVERSE_S(282, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_282(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_282(op, st, BOOST_PP_SEQ_REVERSE_S(283, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_283(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_283(op, st, BOOST_PP_SEQ_REVERSE_S(284, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_284(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_284(op, st, BOOST_PP_SEQ_REVERSE_S(285, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_285(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_285(op, st, BOOST_PP_SEQ_REVERSE_S(286, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_286(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_286(op, st, BOOST_PP_SEQ_REVERSE_S(287, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_287(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_287(op, st, BOOST_PP_SEQ_REVERSE_S(288, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_288(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_288(op, st, BOOST_PP_SEQ_REVERSE_S(289, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_289(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_289(op, st, BOOST_PP_SEQ_REVERSE_S(290, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_290(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_290(op, st, BOOST_PP_SEQ_REVERSE_S(291, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_291(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_291(op, st, BOOST_PP_SEQ_REVERSE_S(292, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_292(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_292(op, st, BOOST_PP_SEQ_REVERSE_S(293, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_293(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_293(op, st, BOOST_PP_SEQ_REVERSE_S(294, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_294(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_294(op, st, BOOST_PP_SEQ_REVERSE_S(295, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_295(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_295(op, st, BOOST_PP_SEQ_REVERSE_S(296, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_296(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_296(op, st, BOOST_PP_SEQ_REVERSE_S(297, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_297(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_297(op, st, BOOST_PP_SEQ_REVERSE_S(298, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_298(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_298(op, st, BOOST_PP_SEQ_REVERSE_S(299, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_299(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_299(op, st, BOOST_PP_SEQ_REVERSE_S(300, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_300(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_300(op, st, BOOST_PP_SEQ_REVERSE_S(301, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_301(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_301(op, st, BOOST_PP_SEQ_REVERSE_S(302, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_302(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_302(op, st, BOOST_PP_SEQ_REVERSE_S(303, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_303(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_303(op, st, BOOST_PP_SEQ_REVERSE_S(304, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_304(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_304(op, st, BOOST_PP_SEQ_REVERSE_S(305, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_305(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_305(op, st, BOOST_PP_SEQ_REVERSE_S(306, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_306(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_306(op, st, BOOST_PP_SEQ_REVERSE_S(307, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_307(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_307(op, st, BOOST_PP_SEQ_REVERSE_S(308, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_308(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_308(op, st, BOOST_PP_SEQ_REVERSE_S(309, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_309(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_309(op, st, BOOST_PP_SEQ_REVERSE_S(310, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_310(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_310(op, st, BOOST_PP_SEQ_REVERSE_S(311, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_311(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_311(op, st, BOOST_PP_SEQ_REVERSE_S(312, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_312(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_312(op, st, BOOST_PP_SEQ_REVERSE_S(313, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_313(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_313(op, st, BOOST_PP_SEQ_REVERSE_S(314, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_314(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_314(op, st, BOOST_PP_SEQ_REVERSE_S(315, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_315(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_315(op, st, BOOST_PP_SEQ_REVERSE_S(316, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_316(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_316(op, st, BOOST_PP_SEQ_REVERSE_S(317, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_317(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_317(op, st, BOOST_PP_SEQ_REVERSE_S(318, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_318(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_318(op, st, BOOST_PP_SEQ_REVERSE_S(319, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_319(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_319(op, st, BOOST_PP_SEQ_REVERSE_S(320, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_320(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_320(op, st, BOOST_PP_SEQ_REVERSE_S(321, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_321(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_321(op, st, BOOST_PP_SEQ_REVERSE_S(322, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_322(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_322(op, st, BOOST_PP_SEQ_REVERSE_S(323, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_323(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_323(op, st, BOOST_PP_SEQ_REVERSE_S(324, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_324(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_324(op, st, BOOST_PP_SEQ_REVERSE_S(325, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_325(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_325(op, st, BOOST_PP_SEQ_REVERSE_S(326, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_326(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_326(op, st, BOOST_PP_SEQ_REVERSE_S(327, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_327(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_327(op, st, BOOST_PP_SEQ_REVERSE_S(328, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_328(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_328(op, st, BOOST_PP_SEQ_REVERSE_S(329, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_329(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_329(op, st, BOOST_PP_SEQ_REVERSE_S(330, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_330(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_330(op, st, BOOST_PP_SEQ_REVERSE_S(331, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_331(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_331(op, st, BOOST_PP_SEQ_REVERSE_S(332, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_332(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_332(op, st, BOOST_PP_SEQ_REVERSE_S(333, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_333(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_333(op, st, BOOST_PP_SEQ_REVERSE_S(334, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_334(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_334(op, st, BOOST_PP_SEQ_REVERSE_S(335, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_335(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_335(op, st, BOOST_PP_SEQ_REVERSE_S(336, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_336(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_336(op, st, BOOST_PP_SEQ_REVERSE_S(337, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_337(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_337(op, st, BOOST_PP_SEQ_REVERSE_S(338, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_338(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_338(op, st, BOOST_PP_SEQ_REVERSE_S(339, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_339(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_339(op, st, BOOST_PP_SEQ_REVERSE_S(340, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_340(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_340(op, st, BOOST_PP_SEQ_REVERSE_S(341, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_341(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_341(op, st, BOOST_PP_SEQ_REVERSE_S(342, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_342(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_342(op, st, BOOST_PP_SEQ_REVERSE_S(343, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_343(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_343(op, st, BOOST_PP_SEQ_REVERSE_S(344, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_344(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_344(op, st, BOOST_PP_SEQ_REVERSE_S(345, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_345(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_345(op, st, BOOST_PP_SEQ_REVERSE_S(346, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_346(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_346(op, st, BOOST_PP_SEQ_REVERSE_S(347, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_347(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_347(op, st, BOOST_PP_SEQ_REVERSE_S(348, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_348(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_348(op, st, BOOST_PP_SEQ_REVERSE_S(349, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_349(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_349(op, st, BOOST_PP_SEQ_REVERSE_S(350, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_350(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_350(op, st, BOOST_PP_SEQ_REVERSE_S(351, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_351(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_351(op, st, BOOST_PP_SEQ_REVERSE_S(352, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_352(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_352(op, st, BOOST_PP_SEQ_REVERSE_S(353, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_353(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_353(op, st, BOOST_PP_SEQ_REVERSE_S(354, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_354(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_354(op, st, BOOST_PP_SEQ_REVERSE_S(355, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_355(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_355(op, st, BOOST_PP_SEQ_REVERSE_S(356, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_356(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_356(op, st, BOOST_PP_SEQ_REVERSE_S(357, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_357(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_357(op, st, BOOST_PP_SEQ_REVERSE_S(358, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_358(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_358(op, st, BOOST_PP_SEQ_REVERSE_S(359, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_359(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_359(op, st, BOOST_PP_SEQ_REVERSE_S(360, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_360(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_360(op, st, BOOST_PP_SEQ_REVERSE_S(361, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_361(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_361(op, st, BOOST_PP_SEQ_REVERSE_S(362, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_362(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_362(op, st, BOOST_PP_SEQ_REVERSE_S(363, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_363(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_363(op, st, BOOST_PP_SEQ_REVERSE_S(364, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_364(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_364(op, st, BOOST_PP_SEQ_REVERSE_S(365, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_365(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_365(op, st, BOOST_PP_SEQ_REVERSE_S(366, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_366(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_366(op, st, BOOST_PP_SEQ_REVERSE_S(367, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_367(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_367(op, st, BOOST_PP_SEQ_REVERSE_S(368, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_368(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_368(op, st, BOOST_PP_SEQ_REVERSE_S(369, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_369(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_369(op, st, BOOST_PP_SEQ_REVERSE_S(370, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_370(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_370(op, st, BOOST_PP_SEQ_REVERSE_S(371, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_371(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_371(op, st, BOOST_PP_SEQ_REVERSE_S(372, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_372(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_372(op, st, BOOST_PP_SEQ_REVERSE_S(373, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_373(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_373(op, st, BOOST_PP_SEQ_REVERSE_S(374, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_374(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_374(op, st, BOOST_PP_SEQ_REVERSE_S(375, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_375(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_375(op, st, BOOST_PP_SEQ_REVERSE_S(376, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_376(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_376(op, st, BOOST_PP_SEQ_REVERSE_S(377, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_377(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_377(op, st, BOOST_PP_SEQ_REVERSE_S(378, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_378(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_378(op, st, BOOST_PP_SEQ_REVERSE_S(379, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_379(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_379(op, st, BOOST_PP_SEQ_REVERSE_S(380, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_380(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_380(op, st, BOOST_PP_SEQ_REVERSE_S(381, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_381(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_381(op, st, BOOST_PP_SEQ_REVERSE_S(382, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_382(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_382(op, st, BOOST_PP_SEQ_REVERSE_S(383, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_383(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_383(op, st, BOOST_PP_SEQ_REVERSE_S(384, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_384(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_384(op, st, BOOST_PP_SEQ_REVERSE_S(385, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_385(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_385(op, st, BOOST_PP_SEQ_REVERSE_S(386, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_386(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_386(op, st, BOOST_PP_SEQ_REVERSE_S(387, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_387(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_387(op, st, BOOST_PP_SEQ_REVERSE_S(388, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_388(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_388(op, st, BOOST_PP_SEQ_REVERSE_S(389, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_389(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_389(op, st, BOOST_PP_SEQ_REVERSE_S(390, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_390(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_390(op, st, BOOST_PP_SEQ_REVERSE_S(391, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_391(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_391(op, st, BOOST_PP_SEQ_REVERSE_S(392, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_392(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_392(op, st, BOOST_PP_SEQ_REVERSE_S(393, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_393(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_393(op, st, BOOST_PP_SEQ_REVERSE_S(394, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_394(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_394(op, st, BOOST_PP_SEQ_REVERSE_S(395, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_395(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_395(op, st, BOOST_PP_SEQ_REVERSE_S(396, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_396(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_396(op, st, BOOST_PP_SEQ_REVERSE_S(397, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_397(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_397(op, st, BOOST_PP_SEQ_REVERSE_S(398, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_398(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_398(op, st, BOOST_PP_SEQ_REVERSE_S(399, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_399(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_399(op, st, BOOST_PP_SEQ_REVERSE_S(400, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_400(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_400(op, st, BOOST_PP_SEQ_REVERSE_S(401, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_401(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_401(op, st, BOOST_PP_SEQ_REVERSE_S(402, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_402(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_402(op, st, BOOST_PP_SEQ_REVERSE_S(403, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_403(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_403(op, st, BOOST_PP_SEQ_REVERSE_S(404, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_404(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_404(op, st, BOOST_PP_SEQ_REVERSE_S(405, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_405(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_405(op, st, BOOST_PP_SEQ_REVERSE_S(406, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_406(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_406(op, st, BOOST_PP_SEQ_REVERSE_S(407, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_407(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_407(op, st, BOOST_PP_SEQ_REVERSE_S(408, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_408(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_408(op, st, BOOST_PP_SEQ_REVERSE_S(409, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_409(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_409(op, st, BOOST_PP_SEQ_REVERSE_S(410, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_410(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_410(op, st, BOOST_PP_SEQ_REVERSE_S(411, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_411(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_411(op, st, BOOST_PP_SEQ_REVERSE_S(412, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_412(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_412(op, st, BOOST_PP_SEQ_REVERSE_S(413, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_413(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_413(op, st, BOOST_PP_SEQ_REVERSE_S(414, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_414(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_414(op, st, BOOST_PP_SEQ_REVERSE_S(415, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_415(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_415(op, st, BOOST_PP_SEQ_REVERSE_S(416, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_416(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_416(op, st, BOOST_PP_SEQ_REVERSE_S(417, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_417(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_417(op, st, BOOST_PP_SEQ_REVERSE_S(418, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_418(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_418(op, st, BOOST_PP_SEQ_REVERSE_S(419, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_419(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_419(op, st, BOOST_PP_SEQ_REVERSE_S(420, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_420(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_420(op, st, BOOST_PP_SEQ_REVERSE_S(421, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_421(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_421(op, st, BOOST_PP_SEQ_REVERSE_S(422, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_422(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_422(op, st, BOOST_PP_SEQ_REVERSE_S(423, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_423(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_423(op, st, BOOST_PP_SEQ_REVERSE_S(424, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_424(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_424(op, st, BOOST_PP_SEQ_REVERSE_S(425, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_425(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_425(op, st, BOOST_PP_SEQ_REVERSE_S(426, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_426(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_426(op, st, BOOST_PP_SEQ_REVERSE_S(427, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_427(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_427(op, st, BOOST_PP_SEQ_REVERSE_S(428, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_428(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_428(op, st, BOOST_PP_SEQ_REVERSE_S(429, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_429(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_429(op, st, BOOST_PP_SEQ_REVERSE_S(430, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_430(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_430(op, st, BOOST_PP_SEQ_REVERSE_S(431, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_431(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_431(op, st, BOOST_PP_SEQ_REVERSE_S(432, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_432(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_432(op, st, BOOST_PP_SEQ_REVERSE_S(433, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_433(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_433(op, st, BOOST_PP_SEQ_REVERSE_S(434, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_434(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_434(op, st, BOOST_PP_SEQ_REVERSE_S(435, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_435(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_435(op, st, BOOST_PP_SEQ_REVERSE_S(436, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_436(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_436(op, st, BOOST_PP_SEQ_REVERSE_S(437, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_437(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_437(op, st, BOOST_PP_SEQ_REVERSE_S(438, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_438(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_438(op, st, BOOST_PP_SEQ_REVERSE_S(439, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_439(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_439(op, st, BOOST_PP_SEQ_REVERSE_S(440, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_440(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_440(op, st, BOOST_PP_SEQ_REVERSE_S(441, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_441(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_441(op, st, BOOST_PP_SEQ_REVERSE_S(442, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_442(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_442(op, st, BOOST_PP_SEQ_REVERSE_S(443, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_443(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_443(op, st, BOOST_PP_SEQ_REVERSE_S(444, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_444(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_444(op, st, BOOST_PP_SEQ_REVERSE_S(445, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_445(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_445(op, st, BOOST_PP_SEQ_REVERSE_S(446, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_446(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_446(op, st, BOOST_PP_SEQ_REVERSE_S(447, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_447(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_447(op, st, BOOST_PP_SEQ_REVERSE_S(448, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_448(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_448(op, st, BOOST_PP_SEQ_REVERSE_S(449, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_449(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_449(op, st, BOOST_PP_SEQ_REVERSE_S(450, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_450(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_450(op, st, BOOST_PP_SEQ_REVERSE_S(451, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_451(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_451(op, st, BOOST_PP_SEQ_REVERSE_S(452, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_452(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_452(op, st, BOOST_PP_SEQ_REVERSE_S(453, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_453(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_453(op, st, BOOST_PP_SEQ_REVERSE_S(454, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_454(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_454(op, st, BOOST_PP_SEQ_REVERSE_S(455, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_455(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_455(op, st, BOOST_PP_SEQ_REVERSE_S(456, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_456(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_456(op, st, BOOST_PP_SEQ_REVERSE_S(457, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_457(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_457(op, st, BOOST_PP_SEQ_REVERSE_S(458, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_458(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_458(op, st, BOOST_PP_SEQ_REVERSE_S(459, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_459(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_459(op, st, BOOST_PP_SEQ_REVERSE_S(460, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_460(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_460(op, st, BOOST_PP_SEQ_REVERSE_S(461, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_461(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_461(op, st, BOOST_PP_SEQ_REVERSE_S(462, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_462(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_462(op, st, BOOST_PP_SEQ_REVERSE_S(463, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_463(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_463(op, st, BOOST_PP_SEQ_REVERSE_S(464, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_464(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_464(op, st, BOOST_PP_SEQ_REVERSE_S(465, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_465(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_465(op, st, BOOST_PP_SEQ_REVERSE_S(466, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_466(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_466(op, st, BOOST_PP_SEQ_REVERSE_S(467, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_467(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_467(op, st, BOOST_PP_SEQ_REVERSE_S(468, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_468(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_468(op, st, BOOST_PP_SEQ_REVERSE_S(469, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_469(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_469(op, st, BOOST_PP_SEQ_REVERSE_S(470, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_470(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_470(op, st, BOOST_PP_SEQ_REVERSE_S(471, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_471(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_471(op, st, BOOST_PP_SEQ_REVERSE_S(472, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_472(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_472(op, st, BOOST_PP_SEQ_REVERSE_S(473, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_473(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_473(op, st, BOOST_PP_SEQ_REVERSE_S(474, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_474(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_474(op, st, BOOST_PP_SEQ_REVERSE_S(475, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_475(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_475(op, st, BOOST_PP_SEQ_REVERSE_S(476, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_476(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_476(op, st, BOOST_PP_SEQ_REVERSE_S(477, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_477(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_477(op, st, BOOST_PP_SEQ_REVERSE_S(478, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_478(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_478(op, st, BOOST_PP_SEQ_REVERSE_S(479, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_479(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_479(op, st, BOOST_PP_SEQ_REVERSE_S(480, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_480(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_480(op, st, BOOST_PP_SEQ_REVERSE_S(481, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_481(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_481(op, st, BOOST_PP_SEQ_REVERSE_S(482, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_482(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_482(op, st, BOOST_PP_SEQ_REVERSE_S(483, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_483(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_483(op, st, BOOST_PP_SEQ_REVERSE_S(484, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_484(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_484(op, st, BOOST_PP_SEQ_REVERSE_S(485, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_485(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_485(op, st, BOOST_PP_SEQ_REVERSE_S(486, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_486(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_486(op, st, BOOST_PP_SEQ_REVERSE_S(487, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_487(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_487(op, st, BOOST_PP_SEQ_REVERSE_S(488, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_488(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_488(op, st, BOOST_PP_SEQ_REVERSE_S(489, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_489(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_489(op, st, BOOST_PP_SEQ_REVERSE_S(490, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_490(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_490(op, st, BOOST_PP_SEQ_REVERSE_S(491, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_491(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_491(op, st, BOOST_PP_SEQ_REVERSE_S(492, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_492(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_492(op, st, BOOST_PP_SEQ_REVERSE_S(493, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_493(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_493(op, st, BOOST_PP_SEQ_REVERSE_S(494, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_494(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_494(op, st, BOOST_PP_SEQ_REVERSE_S(495, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_495(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_495(op, st, BOOST_PP_SEQ_REVERSE_S(496, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_496(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_496(op, st, BOOST_PP_SEQ_REVERSE_S(497, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_497(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_497(op, st, BOOST_PP_SEQ_REVERSE_S(498, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_498(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_498(op, st, BOOST_PP_SEQ_REVERSE_S(499, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_499(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_499(op, st, BOOST_PP_SEQ_REVERSE_S(500, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_500(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_500(op, st, BOOST_PP_SEQ_REVERSE_S(501, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_501(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_501(op, st, BOOST_PP_SEQ_REVERSE_S(502, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_502(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_502(op, st, BOOST_PP_SEQ_REVERSE_S(503, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_503(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_503(op, st, BOOST_PP_SEQ_REVERSE_S(504, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_504(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_504(op, st, BOOST_PP_SEQ_REVERSE_S(505, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_505(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_505(op, st, BOOST_PP_SEQ_REVERSE_S(506, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_506(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_506(op, st, BOOST_PP_SEQ_REVERSE_S(507, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_507(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_507(op, st, BOOST_PP_SEQ_REVERSE_S(508, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_508(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_508(op, st, BOOST_PP_SEQ_REVERSE_S(509, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_509(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_509(op, st, BOOST_PP_SEQ_REVERSE_S(510, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_510(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_510(op, st, BOOST_PP_SEQ_REVERSE_S(511, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_511(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_511(op, st, BOOST_PP_SEQ_REVERSE_S(512, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_512(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_512(op, st, BOOST_PP_SEQ_REVERSE_S(513, ss), BOOST_PP_SEQ_SIZE(ss))
#
# endif
