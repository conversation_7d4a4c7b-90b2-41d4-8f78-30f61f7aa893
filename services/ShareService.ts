import Share from 'react-native-share';
import { Recipe, InstructionType, Ingredient } from '@/components/types';

export class ShareService {
  /**
   * Share a recipe with title, ingredients, and instructions
   */
  static async shareRecipe(
    recipe: Recipe,
    instructionType: InstructionType = InstructionType.DETAILED,
    servings: number = 1
  ): Promise<void> {
    try {
      const shareContent = this.formatRecipeForSharing(recipe, instructionType, servings);

      const shareOptions = {
        title: `${recipe.title} Recipe`,
        message: shareContent,
        subject: `Check out this ${recipe.title} recipe!`, // For email sharing
      };

      await Share.open(shareOptions);
    } catch (error) {
      // User cancelled sharing or an error occurred
      if (error instanceof Error && error.message !== 'User did not share') {
        console.error('Error sharing recipe:', error);
        throw error;
      }
    }
  }

  /**
   * Format recipe data into a shareable text format
   */
  private static formatRecipeForSharing(recipe: Recipe, instructionType: InstructionType, servings: number): string {
    const { title, timeInMinutes, calories, ingredients, instructions } = recipe;

    // Format ingredients list
    const ingredientsList = ingredients.map((ingredient: Ingredient) => `• ${ingredient.name}`).join('\n');

    // Get the appropriate instructions based on type
    const recipeInstructions = instructions[instructionType] || instructions[InstructionType.DETAILED];

    // Format the complete recipe
    const formattedRecipe = `🍽️ ${title}

⏱️ Time: ${timeInMinutes} minutes
🔥 Calories: ${calories} per serving
👥 Servings: ${servings}

📝 INGREDIENTS:
${ingredientsList}

👨‍🍳 INSTRUCTIONS:
${recipeInstructions}

---
Shared from the ChefPal app 👨‍🍳`;

    return formattedRecipe;
  }

  /**
   * Share a basic recipe (without detailed ingredients/instructions)
   * Used for collapsed recipe cards
   */
  static async shareBasicRecipe(recipe: Recipe): Promise<void> {
    try {
      const shareContent = this.formatBasicRecipeForSharing(recipe);

      const shareOptions = {
        title: `${recipe.title} Recipe`,
        message: shareContent,
        subject: `Check out this ${recipe.title} recipe!`,
      };

      await Share.open(shareOptions);
    } catch (error) {
      if (error instanceof Error && error.message !== 'User did not share') {
        console.error('Error sharing basic recipe:', error);
        throw error;
      }
    }
  }

  /**
   * Format basic recipe info for sharing (without ingredients/instructions)
   */
  private static formatBasicRecipeForSharing(recipe: Recipe): string {
    const { title, timeInMinutes, calories } = recipe;

    const formattedRecipe = `🍽️ ${title}

⏱️ Time: ${timeInMinutes} minutes
🔥 Calories: ${calories} per serving

This looks delicious! Get the full recipe and more cooking tips with ChefPal 👨‍🍳`;

    return formattedRecipe;
  }
}
