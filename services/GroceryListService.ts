import { auth } from '@/firebase/firebaseConfig';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { Tag, GroceryItem } from '@/components/types';

/**
 * Service for managing grocery list items
 */
export class GroceryListService {
  /**
   * Get the grocery list for the current user
   */
  static async getGroceryList(): Promise<GroceryItem[]> {
    try {
      const user = auth.currentUser;
      if (!user) {
        console.log('Cannot get grocery list: User not authenticated');
        return [];
      }

      const groceryListDoc = await firestoreRepository.getDocument(FirestoreCollections.GROCERY_LIST, user.uid);

      if (!groceryListDoc || !groceryListDoc.items) {
        return [];
      }

      // Ensure all items have a tags array
      return (groceryListDoc.items as GroceryItem[]).map((item) => ({
        ...item,
        tags: item.tags || [],
      }));
    } catch (error) {
      console.error('Error getting grocery list:', error);
      return [];
    }
  }

  /**
   * Save the grocery list for the current user
   */
  static async saveGroceryList(items: GroceryItem[]): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        console.log('Cannot save grocery list: User not authenticated');
        return;
      }

      await firestoreRepository.addOrReplaceDocument(FirestoreCollections.GROCERY_LIST, user.uid, { items });
    } catch (error) {
      console.error('Error saving grocery list:', error);
      throw error;
    }
  }

  /**
   * Add an item to the grocery list
   * @param itemName Name of the item to add
   * @param tags Optional tags for the item
   * @returns The updated grocery list
   */
  static async addItem(itemName: string, tags: Tag[] = []): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      // Check if item already exists
      const existingItem = groceryList.find((item) => item.name.toLowerCase() === itemName.toLowerCase());

      if (existingItem) {
        // Item already exists, don't add a duplicate
        return groceryList;
      }

      // Create new item
      const newItem: GroceryItem = {
        name: itemName.trim(),
        checked: false,
        quantity: 1,
        tags: tags,
      };

      // Add to list
      const updatedList = [...groceryList, newItem];
      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error adding item to grocery list:', error);
      throw error;
    }
  }

  /**
   * Add multiple items to the grocery list
   * @param itemNames Array of item names to add
   * @param tags Optional tags to apply to all items
   * @returns The updated grocery list
   */
  static async addItems(itemNames: string[], tags: Tag[] = []): Promise<GroceryItem[]> {
    try {
      let groceryList = await this.getGroceryList();

      // Filter out items that already exist in the list
      const newItemNames = itemNames.filter(
        (name) => !groceryList.some((item) => item.name.toLowerCase() === name.toLowerCase())
      );

      // Create new items
      const newItems: GroceryItem[] = newItemNames.map((name) => ({
        name: name.trim(),
        checked: false,
        quantity: 1,
        tags: tags,
      }));

      // Add to list if there are any new items
      if (newItems.length > 0) {
        const updatedList = [...groceryList, ...newItems];
        await this.saveGroceryList(updatedList);
        return updatedList;
      }

      return groceryList;
    } catch (error) {
      console.error('Error adding items to grocery list:', error);
      throw error;
    }
  }

  /**
   * Toggle the checked status of an item
   * @param itemName Name of the item to toggle
   * @returns The updated grocery list
   */
  static async toggleItemChecked(itemName: string): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      const updatedList = groceryList.map((item) =>
        item.name.toLowerCase() === itemName.toLowerCase() ? { ...item, checked: !item.checked } : item
      );

      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error toggling item checked status:', error);
      throw error;
    }
  }

  /**
   * Remove checked items from the grocery list
   * @returns The updated grocery list
   */
  static async removeCheckedItems(): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      const updatedList = groceryList.filter((item) => !item.checked);

      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error removing checked items:', error);
      throw error;
    }
  }

  /**
   * Update tags for an item
   * @param itemName Name of the item to update
   * @param tags New tags for the item
   * @returns The updated grocery list
   */
  static async updateItemTags(itemName: string, tags: Tag[]): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      const updatedList = groceryList.map((item) =>
        item.name.toLowerCase() === itemName.toLowerCase() ? { ...item, tags } : item
      );

      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error updating item tags:', error);
      throw error;
    }
  }

  /**
   * Increment the quantity of an item
   * @param itemName Name of the item to increment
   * @returns The updated grocery list
   */
  static async incrementItemQuantity(itemName: string): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      const updatedList = groceryList.map((item) =>
        item.name.toLowerCase() === itemName.toLowerCase() ? { ...item, quantity: item.quantity + 1 } : item
      );

      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error incrementing item quantity:', error);
      throw error;
    }
  }

  /**
   * Decrement the quantity of an item
   * @param itemName Name of the item to decrement
   * @returns The updated grocery list
   */
  static async decrementItemQuantity(itemName: string): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      const updatedList = groceryList.map((item) => {
        if (item.name.toLowerCase() === itemName.toLowerCase()) {
          const newQuantity = Math.max(1, item.quantity - 1);
          return { ...item, quantity: newQuantity };
        }
        return item;
      });

      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error decrementing item quantity:', error);
      throw error;
    }
  }

  /**
   * Remove a specific item from the grocery list
   * @param itemName Name of the item to remove
   * @returns The updated grocery list
   */
  static async removeItem(itemName: string): Promise<GroceryItem[]> {
    try {
      const groceryList = await this.getGroceryList();

      const updatedList = groceryList.filter((item) => item.name.toLowerCase() !== itemName.toLowerCase());

      await this.saveGroceryList(updatedList);
      return updatedList;
    } catch (error) {
      console.error('Error removing item:', error);
      throw error;
    }
  }
}
